/* Random Note View Styles */
.random-note-view {
    padding: 16px;
    height: 100%;
    overflow-y: auto;
}

.random-note-header {
    margin-bottom: 20px;
    border-bottom: 1px solid var(--background-modifier-border);
    padding-bottom: 16px;
}

.random-note-header h2 {
    margin: 0 0 16px 0;
}

.random-note-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    align-items: center;
}

.control-item {
    display: flex;
    align-items: center;
    gap: 6px;
}

.control-item select,
.control-item input {
    padding: 4px 8px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    background: var(--background-primary);
    font-size: 14px;
}

.random-notes-container {
    margin-top: 20px;
}

.random-note-info {
    margin-bottom: 16px;
    padding: 8px 12px;
    background: var(--background-secondary);
    border-radius: 6px;
    font-size: 14px;
    color: var(--text-muted);
}

/* 瀑布流布局 */
.notes-waterfall {
    column-count: auto;
    column-width: 280px;
    column-gap: 16px;
    margin-top: 16px;
}

.random-note-card {
    padding: 12px;
    margin-bottom: 16px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 6px;
    background: var(--background-primary);
    cursor: pointer;
    transition: all 0.2s ease;
    break-inside: avoid;
    display: inline-block;
    width: 100%;
    box-sizing: border-box;
}

.random-note-card:hover {
    border-color: var(--interactive-accent);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.random-note-card:active {
    transform: translateY(0);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.note-card-title {
    margin: 0 0 8px 0;
    font-size: 15px;
    line-height: 1.3;
}

.note-title {
    font-weight: 600;
}

.note-id {
    color: var(--text-muted);
    font-size: 13px;
}

.note-card-content p {
    margin: 0;
    color: var(--text-muted);
    font-size: 13px;
    line-height: 1.4;
}

.random-note-empty,
.random-note-error {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-muted);
    font-style: italic;
}

.random-note-error {
    color: var(--text-error);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .notes-waterfall {
        column-width: 240px;
        column-gap: 12px;
    }

    .random-note-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .control-item {
        flex-direction: column;
        align-items: stretch;
        gap: 4px;
    }
}

@media (max-width: 480px) {
    .notes-waterfall {
        column-count: 1;
        column-width: auto;
    }

    .random-note-view {
        padding: 12px;
    }
}

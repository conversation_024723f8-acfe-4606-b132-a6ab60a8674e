
import { App, Modal, Notice, Plugin, PluginSettingTab, Setting, TFile, normalizePath, ItemView, WorkspaceLeaf } from 'obsidian';


export const RANDOM_NOTE_VIEW_TYPE = "random-note-view";

// 随机笔记视图类
export class RandomNoteView extends ItemView {
	plugin: any;

	constructor(leaf: WorkspaceLeaf, plugin: any) {
		super(leaf);
		this.plugin = plugin;
	}

	getViewType() {
		return RANDOM_NOTE_VIEW_TYPE;
	}

	getDisplayText() {
		return "Random Notes";
	}

	getIcon() {
		return "dice";
	}

	async onOpen() {
		const container = this.containerEl.children[1];
		container.empty();
		container.addClass('random-note-view');

		this.renderView();
	}

	async onClose() {
		// 清理工作
	}

	async renderView() {
		const container = this.containerEl.children[1];
		container.empty();

		// 创建标题和控制区域
		const headerEl = container.createEl('div', { cls: 'random-note-header' });
		headerEl.createEl('h2', { text: 'Random Notes' });

		// 创建控制面板
		const controlsEl = headerEl.createEl('div', { cls: 'random-note-controls' });

		// 数量选择器
		const countContainer = controlsEl.createEl('div', { cls: 'control-item' });
		countContainer.createEl('label', { text: 'Count: ' });
		const countSelect = countContainer.createEl('select');
		for (let i = 2; i <= 10; i++) {
			const option = countSelect.createEl('option', { value: i.toString(), text: i.toString() });
			if (i === this.plugin.settings.randomNoteCount) {
				option.selected = true;
			}
		}

		// ID前缀输入框
		const prefixContainer = controlsEl.createEl('div', { cls: 'control-item' });
		prefixContainer.createEl('label', { text: 'ID Prefixes: ' });
		const prefixInput = prefixContainer.createEl('input', {
			type: 'text',
			placeholder: 'e.g., 1,2.1,3',
			value: this.plugin.settings.randomNoteIdPrefixes
		});

		// 抽取按钮
		const drawButton = controlsEl.createEl('button', { text: 'Draw Random Notes', cls: 'mod-cta' });

		// 笔记显示区域
		const notesContainer = container.createEl('div', { cls: 'random-notes-container' });

		// 事件监听
		drawButton.addEventListener('click', async () => {
			const count = parseInt(countSelect.value);
			const prefixes = prefixInput.value;

			// 更新设置
			this.plugin.settings.randomNoteCount = count;
			this.plugin.settings.randomNoteIdPrefixes = prefixes;
			await this.plugin.saveSettings();

			// 抽取并显示笔记
			await this.drawRandomNotes(notesContainer, count, prefixes);
		});

		// 初始加载
		await this.drawRandomNotes(notesContainer, this.plugin.settings.randomNoteCount, this.plugin.settings.randomNoteIdPrefixes);
	}

	async drawRandomNotes(container: HTMLElement, count: number, prefixesStr: string) {
		container.empty();

		try {
			const randomNotes = await this.getRandomNotes(count, prefixesStr);

			if (randomNotes.length === 0) {
				container.createEl('div', {
					text: 'No notes found matching the criteria.',
					cls: 'random-note-empty'
				});
				return;
			}

			// 显示找到的笔记数量
			const infoEl = container.createEl('div', { cls: 'random-note-info' });
			infoEl.createEl('span', { text: `Found ${randomNotes.length} random notes:` });

			// 创建瀑布流容器
			const waterfallContainer = container.createEl('div', { cls: 'notes-waterfall' });

			// 创建笔记卡片
			for (const note of randomNotes) {
				await this.createNoteCard(waterfallContainer, note);
			}

		} catch (error) {
			console.error('Error drawing random notes:', error);
			container.createEl('div', {
				text: 'Error loading random notes. Check console for details.',
				cls: 'random-note-error'
			});
		}
	}

	async getRandomNotes(count: number, prefixesStr: string): Promise<TFile[]> {
		const allFiles = this.app.vault.getMarkdownFiles();
		const prefixes = prefixesStr.trim() ? prefixesStr.split(',').map(p => p.trim()) : [];

		// 过滤有ID的笔记
		const notesWithId: TFile[] = [];

		for (const file of allFiles) {
			const cache = this.app.metadataCache.getFileCache(file);
			if (cache?.frontmatter?.id) {
				const noteId = String(cache.frontmatter.id);

				// 如果指定了前缀，检查是否匹配
				if (prefixes.length > 0) {
					const matchesPrefix = prefixes.some(prefix => noteId.startsWith(prefix));
					if (matchesPrefix) {
						notesWithId.push(file);
					}
				} else {
					// 没有指定前缀，包含所有有ID的笔记
					notesWithId.push(file);
				}
			}
		}

		// 随机抽取
		const shuffled = this.shuffleArray(notesWithId);
		return shuffled.slice(0, Math.min(count, shuffled.length));
	}

	async createNoteCard(container: HTMLElement, file: TFile) {
		const cardEl = container.createEl('div', { cls: 'random-note-card' });

		// 获取笔记元数据
		const cache = this.app.metadataCache.getFileCache(file);
		const noteId = cache?.frontmatter?.id || 'No ID';
		const noteTitle = file.basename;

		// 获取笔记内容预览
		const content = await this.app.vault.read(file);
		const preview = this.extractPreview(content);

		// 卡片标题区域
		const headerEl = cardEl.createEl('div', { cls: 'note-card-header' });
		const titleEl = headerEl.createEl('h3', { cls: 'note-card-title' });
		titleEl.createEl('span', { text: noteTitle, cls: 'note-title' });
		titleEl.createEl('span', { text: ` (ID: ${noteId})`, cls: 'note-id' });

		// 卡片内容预览
		if (preview) {
			const contentEl = cardEl.createEl('div', { cls: 'note-card-content' });
			contentEl.createEl('p', { text: preview });
		}

		// 点击卡片打开笔记
		cardEl.addEventListener('click', async (e) => {
			try {
				// 阻止事件冒泡，确保只处理一次点击
				e.stopPropagation();
				e.preventDefault();

				// 获取当前活动的叶子节点，如果没有则创建新的
				const leaf = this.app.workspace.getLeaf(false);
				await leaf.openFile(file);

				// 确保文件被激活
				this.app.workspace.setActiveLeaf(leaf);
			} catch (error) {
				console.error('Error opening note:', error);
				// 显示错误通知
				new Notice(`Failed to open note: ${file.basename}`);
			}
		});
	}

	extractPreview(content: string): string {
		// 移除frontmatter
		const withoutFrontmatter = content.replace(/^---[\s\S]*?---\n?/, '');

		// 移除markdown语法
		const withoutMarkdown = withoutFrontmatter
			.replace(/#{1,6}\s+/g, '') // 标题
			.replace(/\*\*(.*?)\*\*/g, '$1') // 粗体
			.replace(/\*(.*?)\*/g, '$1') // 斜体
			.replace(/`(.*?)`/g, '$1') // 行内代码
			.replace(/\[(.*?)\]\(.*?\)/g, '$1') // 链接
			.replace(/!\[.*?\]\(.*?\)/g, '') // 图片
			.trim();

		// 返回前150个字符
		return withoutMarkdown.length > 150
			? withoutMarkdown.substring(0, 150) + '...'
			: withoutMarkdown;
	}

    shuffleArray(array: TFile[]): TFile[] {
        for (let i = array.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1)); 
            [array[i], array[j]] = [array[j], array[i]];  
        }
        return array;
    }
}
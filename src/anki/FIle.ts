import { AnkiConnectNote, AnkiConnectNoteAndID } from './interfaces/NoteInterface'
import { FileData } from './Configure'
import { CachedMetadata } from 'obsidian'
import { FormatConverter } from './Format'

import * as AnkiConnect from './AnkiConnect'
import { Md5 } from 'ts-md5/dist/md5';
import { Note } from './Note'

function id_to_str(identifier: number){
     let result = "ID: " + identifier.toString()
     return "<!--" + result + "-->"
}

abstract class AbstractFile {
    file: string
    path: string
    url: string
    original_file: string
    data: FileData
    file_cache: CachedMetadata
    
    target_deck:string
    global_tags:string

    notes_to_add: AnkiConnectNote[]
    id_indexed: number[]
    notes_to_edit: AnkiConnectNoteAndID[]
    notes_to_delete: number[]
    all_notes_to_add: AnkiConnectNote[]

    note_ids: Array<number | null>
    card_ids: number[]
    tags: string[]

    formatter : FormatConverter

    constructor(file_contents: string, path: string, url: string, data: FileData, file_cache: CachedMetadata) {
        this.data = data
        this.file = file_contents
        this.path = path
        this.url = url
        this.original_file = this.file
        this.file_cache = file_cache
        this.formatter = new FormatConverter(file_cache, this.data.vault_name)
    }

    getHash(): string {
        return Md5.hashStr(this.file) as string
    }
    
    abstract writeIDs(): void

    getCreateDecks(): AnkiConnect.AnkiConnectRequest {
        let actions: AnkiConnect.AnkiConnectRequest[] = []
        for (let note of this.all_notes_to_add) {
            actions.push(AnkiConnect.createDeck(note.deckName))
        }
        return AnkiConnect.multi(actions)
    }

    getAddNotes(): AnkiConnect.AnkiConnectRequest {
        let actions: AnkiConnect.AnkiConnectRequest[] = []
        for (let note of this.all_notes_to_add) {
            actions.push(AnkiConnect.addNote(note))
        }
        return AnkiConnect.multi(actions)
    }

    getDeleteNotes(): AnkiConnect.AnkiConnectRequest {
        return AnkiConnect.deleteNotes(this.notes_to_delete)
    }

    getUpdateFields(): AnkiConnect.AnkiConnectRequest {
        let actions: AnkiConnect.AnkiConnectRequest[] = []
        for (let parsed of this.notes_to_edit) {
            actions.push(
                AnkiConnect.updateNoteFields(
                    parsed.identifier!, parsed.note.fields
                )
            )
        }
        return AnkiConnect.multi(actions)
    }
        getNoteInfo(): AnkiConnect.AnkiConnectRequest {
        let IDs: number[] = []
        for (let parsed of this.notes_to_edit) {
            IDs.push(parsed.identifier!)
        }
        return AnkiConnect.notesInfo(IDs)
    }

    getChangeDecks(): AnkiConnect.AnkiConnectRequest {
        return AnkiConnect.changeDeck(this.card_ids, this.target_deck)
    }

    getClearTags(): AnkiConnect.AnkiConnectRequest {
        let IDs: number[] = []
        for (let parsed of this.notes_to_edit) {
            IDs.push(parsed.identifier!)
        }
        return AnkiConnect.removeTags(IDs, this.tags.join(" "))
    }

    getAddTags(): AnkiConnect.AnkiConnectRequest {
        let actions: AnkiConnect.AnkiConnectRequest[] = []
        for (let parsed of this.notes_to_edit) {
            actions.push(
                AnkiConnect.addTags([parsed.identifier!], parsed.note.tags.join(" ") + " " + this.global_tags)
            )
        }
        return AnkiConnect.multi(actions)
    }
}

export class File extends AbstractFile{

    scanNotes(){
        for(let note_match of this.file.matchAll(this.data.NOTE_REGEXP)){
            let parsed = new Note(note_match[1],this.formatter).parse(
                this.target_deck,
                this.url,
                this.data,
                this.path
            )
            
            if(parsed.identifier == null){
                this.notes_to_add.push(parsed.note)
            }else{
                this.notes_to_edit.push(parsed)
            }
        }
    }
    
    writeIDs(): void {
        //todo 在笔记最后一行加上id
    }
}
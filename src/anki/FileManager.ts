import { App, CachedMetadata, TAbstractFile, TFile, TFolder } from "obsidian"
import { File } from "./FIle"
import { FileData } from "./Configure"

import * as AnkiConnect from './AnkiConnect'

interface addNoteResponse
{
    result: number,
    error: string | null
}

interface notesInfoResponse {
    result: Array<{
        noteId: number,
        modelName: string,
        tags: string[],
        fields: Record<string,{
            order: number,
            value: string
        }>,
        cards: number[]
    }>,
    error: string | null
}

interface Request1Result {
    0: {
        error: string | null,
        result: Array<{
            result: addNoteResponse[],
            error: string | null
        }>
    },
    1: {
        error: string | null,
        result: notesInfoResponse[]
    },
    2: any,
    3: any,
    4: any
}

export class FileManager {
    app: App
    data:FileData
    files: TFile[]
    ownFiles: Array<File>
    file_hashs: Record<string,string>
    requests_1_result: any
    added_media_set: Set<string>

    constructor(app:App,files:TFile[],file_hashes:Record<string,string>) {
        this.app = app
        this.files = files
        this.file_hashs = file_hashes
        this.ownFiles = []
        this.added_media_set = new Set<string>()
    }

    
    getUrl(file: TFile): string {
        return "obsidian://open?vault=" + encodeURIComponent(this.data.vault_name) + String.raw`&file=` + encodeURIComponent(file.path)
    }

    getFolderPathList(file: TFile): TFolder[]{
        let result: TFolder[] = []
        let abstractFile: TAbstractFile = file
        while(abstractFile && abstractFile.hasOwnProperty('parent')){
            result.push(abstractFile.parent!)
            abstractFile = abstractFile.parent as TAbstractFile
        }
        result.pop() //removes top-level vault
        return result
    }

    dataToFileData(file:TFile) :FileData{
        const folder_path_list:TFolder[] = this.getFolderPathList(file)
        let result: FileData = JSON.parse(JSON.stringify(this.data))

        result.DECK_REGEXP = this.data.DECK_REGEXP
        result.TAG_REGEXP = this.data.TAG_REGEXP
        result.NOTE_REGEXP = this.data.NOTE_REGEXP
        result.template.deckName = "Default"

        return result
    }

    async genFiles(){
        for(let file of this.files){
            const content:string = await this.app.vault.read(file)
            const cache: CachedMetadata = this.app.metadataCache.getFileCache(file)!
            const file_data: FileData = this.dataToFileData(file)
            this.ownFiles.push(
                new File(
                    content,
                    file.path,
                    this.getUrl(file),
                    file_data,
                    cache
                ))
        }
    }

    async request_1(){
        let requests: AnkiConnect.AnkiConnectRequest[] = []
        
    }
}
import { FileData } from './Configure'
import { FormatConverter } from './Format'
import { AnkiConnectNote, AnkiConnectNoteAndID } from './interfaces/NoteInterface'

export const ID_REGEXP_STR: string = String.raw`\n?(?:<!--)?(?:ID: (\d+).*)`
export const TAG_REGEXP_STR: string = String.raw`(Tags: .*)`




abstract class AbstractNote{
    text: string
    split_text: string[]
    current_field_num: number
    delete: boolean
    identifier: number | null
    note_type: string
    field_names: string[]
    current_field: string
    ID_REGEXP: RegExp = /(?:<!--)?ID: (\d+)/
    formatter: FormatConverter

    constructor(note_text:string,formatter:FormatConverter){
        this.text = note_text.trim()
        this.split_text = this.getSplitText()
        this.current_field_num = 0
        this.delete = false
        this.identifier = this.getIdentifier()
        this.note_type = this.getNoteType()
        this.field_names = []
        this.current_field = ""
        this.formatter = formatter
    }

    abstract getSplitText(): string[]

    abstract getIdentifier(): number | null

    abstract getNoteType(): string

    parse(deck:string,url:string,data:FileData,context:string):AnkiConnectNoteAndID{
        let template = JSON.parse(JSON.stringify(data.template))
        template["modelName"] = this.note_type
        template["deckName"] = deck
        template["identifier"] = this.identifier

        return {note: template, identifier: this.identifier}
    }
}

export class Note extends AbstractNote{
    getSplitText(): string[] {
        return this.text.split("\n")
    }


    // 解析最后一行ID
    getIdentifier(): number | null {
        if (this.ID_REGEXP.test(this.split_text[this.split_text.length-1])) {
            return parseInt(this.ID_REGEXP.exec(this.split_text.pop()!)![1])
        } else {
            return null
        }
    }

    getNoteType(): string {
        return this.split_text[0]
    }
}

export class Evernote extends Note{
    note_title: string
    note_content:string

    constructor(note_text:string,file_title:string,formatter:FormatConverter){
        super(note_text,formatter)
        this.note_title = file_title
        this.parseNote(note_text)
    }

    parseNote(text:string) {
        const frontmatterMatch = text.match(/^---\n([\s\S]*?)\n---\n([\s\S]*)$/m)
        this.note_content = text
        //todo

    }
    
    getSplitText(): string[] {
        return this.text.split("\n")
    }



    getNoteType(): string {
        return "Evernote"
    }


    parse(deck:string,url:string, data: FileData, context:string): AnkiConnectNoteAndID {
        const content = this.formatter.format(this.note_content,false,false)
        const template : AnkiConnectNote = {
            deckName: deck,
            modelName: this.note_type,
            fields: {
                "Front": this.note_title,
                "Back": context
            },
            options: {
                allowDuplicate: false,
                duplicateScope: "deck"
            },
            tags: ["Obsidian_to_Anki"]
        }
        return {note: template, identifier: this.identifier}
    }
}
import { FIELDS_DICT } from "./interfaces/FieldInterface";
import { AnkiConnectNote } from "../interface/anki-note-interface";


export interface FileData{
    fields_dict: FIELDS_DICT	
    file_link_fields: Record<string, string>
	context_fields: Record<string, string>
    template: AnkiConnectNote
    EXITSING_IDS: number[]
    vault_name: string

    DECK_REGEXP: RegExp
    TAG_REGEXP: RegExp
    NOTE_REGEXP: RegExp
}
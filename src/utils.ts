import { TFile, TFolder } from 'obsidian';
import { Md5 } from 'ts-md5/dist/md5';

/**
 * Recursively traverse a TFolder and return all TFiles.
 * @param tfolder - The TFolder to start the traversal from.
 * @returns An array of TFiles found within the folder and its subfolders.
 */
export function getAllTFilesInFolder(tfolder: TFolder) {
    const allTFiles: TFile[] = [];
    // Check if the provided object is a TFolder
    if (!(tfolder instanceof TFolder)) {
        return allTFiles;
    }
    // Iterate through the contents of the folder
    tfolder.children.forEach((child) => {
        // If it's a TFile, add it to the result
        if (child instanceof TFile) {
            allTFiles.push(child);
        } else if (child instanceof TFolder) {
            // If it's a TFolder, recursively call the function on it
            const filesInSubfolder = getAllTFilesInFolder(child);
            allTFiles.push(...filesInSubfolder);
        }
        // Ignore other types of files or objects
    });
    return allTFiles;
}

export function getHash(file:TFile): string {
    return Md5.hashStr(file.path) as string
}
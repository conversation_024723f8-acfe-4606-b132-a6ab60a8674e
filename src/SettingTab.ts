import { App,PluginSettingTab, Setting } from 'obsidian';

// 设置接口 - 添加随机抽取相关设置
export interface PluginSettings {
	newNoteFolderPath: string;
	randomNoteCount: number;
	randomNoteIdPrefixes: string;
}


// 设置页面 (不变) 
export class SettingTab extends PluginSettingTab {
	plugin: any;

	constructor(app: App, plugin: any) {
		super(app, plugin);
		this.plugin = plugin;
	}

	display(): void {
		const { containerEl } = this;
		containerEl.empty();
		containerEl.createEl('h1', { text: 'ID Note Creator Settings' });

		new Setting(containerEl)
			.setName('New note folder path')
			.setDesc('Specify the folder to save new notes in. E.g., "Inbox" or "Notes/Zettelkasten".')
			.addText(text => text
				.setPlaceholder('Enter folder path')
				.setValue(this.plugin.settings.newNoteFolderPath)
				.onChange(async (value) => {
					this.plugin.settings.newNoteFolderPath = value;
					await this.plugin.saveSettings();
				}));

		// 随机抽取设置
		containerEl.createEl('h1', { text: 'Random Note Settings' });

		new Setting(containerEl)
			.setName('Random note count')
			.setDesc('Number of random notes to display (2-10)')
			.addSlider(slider => slider
				.setLimits(2, 10, 1)
				.setValue(this.plugin.settings.randomNoteCount)
				.setDynamicTooltip()
				.onChange(async (value) => {
					this.plugin.settings.randomNoteCount = value;
					await this.plugin.saveSettings();
				}));

		new Setting(containerEl)
			.setName('ID prefixes for random selection')
			.setDesc('Specify ID prefixes to filter notes for random selection. Leave empty for all notes. Separate multiple prefixes with commas. E.g., "1,2.1,3"')
			.addTextArea(text => text
				.setPlaceholder('Enter ID prefixes (e.g., 1,2.1,3)')
				.setValue(this.plugin.settings.randomNoteIdPrefixes)
				.onChange(async (value) => {
					this.plugin.settings.randomNoteIdPrefixes = value;
					await this.plugin.saveSettings();
				}));
	}
}
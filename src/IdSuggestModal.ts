import { App, Modal, Notice, normalizePath } from 'obsidian';


// Modal类 (不变)
export class IdSuggestModal extends Modal {
	plugin: any;

	constructor(app: App, plugin: any) {
		super(app);
		this.plugin = plugin;
	}

	onOpen() {
		const { contentEl } = this; // 注意：contentEl是Modal类的属性
		contentEl.empty();

		// 创建标题
		contentEl.createEl('h3', { text: 'Create New Note' });

		// ID前缀输入区域
		const idSection = contentEl.createEl('div');
		idSection.createEl('label', { text: 'ID Prefix:' });
		const idInput = idSection.createEl('input', { type: 'text' });
		idInput.style.width = '100%';
		idInput.style.marginBottom = '10px';
		idInput.placeholder = 'Enter ID prefix (e.g., 1.2)';

		// 笔记标题输入区域
		const titleSection = contentEl.createEl('div');
		titleSection.createEl('label', { text: 'Note Title:' });
		const titleInput = titleSection.createEl('input', { type: 'text' });
		titleInput.style.width = '100%';
		titleInput.style.marginBottom = '10px';
		titleInput.placeholder = 'Enter note title';
		titleInput.value = 'Untitled Note';

		// ID建议显示区域
        const suggestionsEl = contentEl.createEl('div');
		suggestionsEl.style.marginBottom = '10px';
		suggestionsEl.style.fontStyle = 'italic';
		suggestionsEl.style.color = '#666';

		// 创建按钮
		const buttonContainer = contentEl.createEl('div');
		buttonContainer.style.textAlign = 'right';
		const createButton = buttonContainer.createEl('button', { text: 'Create' });
		createButton.style.marginRight = '10px';
		const cancelButton = buttonContainer.createEl('button', { text: 'Cancel' });

		// 设置焦点到ID输入框
		idInput.focus();

		// ID输入框事件监听
		idInput.addEventListener('input', async () => {
			const prefix = idInput.value;
			if (prefix || prefix === '') { // Allow suggesting for empty prefix
				const nextId = await this.findNextId(prefix);
                suggestionsEl.setText(`Suggested ID: ${nextId}`);
			} else {
                suggestionsEl.setText('');
            }
		});

		// 创建笔记的函数
		const createNote = async () => {
			const prefix = idInput.value;
			const noteTitle = titleInput.value.trim() || 'Untitled Note';
			try {
				const nextId = await this.findNextId(prefix);
				await this.createNoteWithId(nextId, noteTitle);
				this.close();
			} catch (error) {
				console.error('Error creating note:', error);
				new Notice('Error creating note. Please check the console log.');
			}
		};

		// 按钮事件监听
		createButton.addEventListener('click', createNote);
		cancelButton.addEventListener('click', () => this.close());

		// 键盘事件监听
		const handleKeydown = async (event: KeyboardEvent) => {
			if (event.key === 'Enter') {
				await createNote();
			} else if (event.key === 'Escape') {
				this.close();
			}
		};

		idInput.addEventListener('keydown', handleKeydown);
		titleInput.addEventListener('keydown', handleKeydown);
	}

	onClose() {
		this.contentEl.empty();
	}


	async findNextId(prefix: string): Promise<string> {
		// 1. 收集库中所有存在的ID到一个Set中，以便进行快速查找 (O(1)复杂度)
		const allFiles = this.app.vault.getMarkdownFiles();
		const existingIds = new Set<string>();
		for (const file of allFiles) {
			const cache = this.app.metadataCache.getFileCache(file);
			if (cache?.frontmatter?.id) {
				existingIds.add(String(cache.frontmatter.id));
			}
		}

		// 处理空前缀的特殊情况，从根级别开始查找
		if (prefix === '') {
			let i = 1;
			while (true) {
				const candidate = String(i);
				if (!existingIds.has(candidate)) {
					return candidate;
				}
				i++;
			}
		}

		const lastCharOfPrefix = prefix.slice(-1);

		// 2. 根据前缀的最后一个字符，决定要生成哪种序列
		
		// 规则A: 如果前缀以'.'结尾, 我们需要生成数字序列 (e.g., "1.2." -> "1.2.1", "1.2.2"...)
		if (lastCharOfPrefix === '.') {
			let i = 1;
			while (true) { // 使用无限循环，直到找到空位
				const candidate = prefix + i;
				if (!existingIds.has(candidate)) {
					return candidate;
				}
				i++;
			}
		}

		// 规则B: 如果前缀以数字结尾, 我们需要生成字母序列 (e.g., "1.2" -> "1.2a", "1.2b"...)
		if (lastCharOfPrefix.match(/[0-9]/)) {
			let i = 0; // 'a' 对应 0, 'b' 对应 1 ...
			while (i < 26) { // 循环26个字母
				const char = String.fromCharCode('a'.charCodeAt(0) + i);
				const candidate = prefix + char;
				if (!existingIds.has(candidate)) {
					return candidate;
				}
				i++;
			}
			// 如果'a'到'z'都用完了, 则按照规则进入下一层级
			return this.findNextId(prefix + 'z');
		}

		// 规则C: 如果前缀以字母结尾, 我们需要生成数字的序列 (e.g., "1.2a" -> "1.2a1", "1.2a2"...)
		if (lastCharOfPrefix.match(/[a-z]/i)) {
			let i = 1;
			while (true) {
				const candidate = prefix + i;
				if (!existingIds.has(candidate)) {
					return candidate;
				}
				i++;
			}
		}

		// 最后的保障: 如果所有规则都不匹配 (例如前缀是特殊字符), 默认行为
		return prefix + '1';
	}
    
	async createNoteWithId(id: string, title: string) {
		const folderPath = this.plugin.settings.newNoteFolderPath;
        if (folderPath && !(this.app.vault.getAbstractFileByPath(normalizePath(folderPath)))) {
            try {
                await this.app.vault.createFolder(folderPath);
                new Notice(`Folder created: ${folderPath}`);
            } catch (error) {
                new Notice(`Error creating folder: ${folderPath}. Saving in root.`);
            }
        }
		const filePath = normalizePath(folderPath ? `${folderPath}/${title}.md` : `${title}.md`);
		
		if (this.app.vault.getAbstractFileByPath(filePath)) {
			new Notice(`File ${filePath} already exists!`); return;
		}

		const content = `---\nid: ${id}\n---\n`;
		const newFile = await this.app.vault.create(filePath, content);
		new Notice(`Successfully created note: id: ${id} title: ${title}`);

		await this.app.workspace.getLeaf(true).openFile(newFile);
	}
}
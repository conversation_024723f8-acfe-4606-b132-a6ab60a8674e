{"name": "obsidian-sample-plugin", "version": "1.0.0", "description": "This is a sample plugin for Obsidian (https://obsidian.md)", "main": "main.js", "scripts": {"dev": "node esbuild.config.mjs", "build": "tsc -noEmit -skipLibCheck && node esbuild.config.mjs production", "version": "node version-bump.mjs && git add manifest.json versions.json", "svelte-check": "svelte-check --tsconfig tsconfig.json"}, "keywords": [], "author": "", "license": "MIT", "devDependencies": {"@types/node": "^16.11.6", "@types/showdown": "^2.0.6", "@typescript-eslint/eslint-plugin": "5.29.0", "@typescript-eslint/parser": "5.29.0", "builtin-modules": "3.3.0", "esbuild": "0.17.3", "esbuild-svelte": "^0.9.3", "obsidian": "latest", "svelte": "^5.35.6", "svelte-check": "^4.2.2", "svelte-preprocess": "^6.0.3", "tslib": "2.4.0", "typescript": "~5.0.0"}, "dependencies": {"showdown": "^2.1.0", "showdown-highlight": "^3.1.0", "ts-md5": "^2.0.1"}}
import { App, Modal, Notice, Plugin, PluginSettingTab, Setting, TFile, normalizePath, ItemView, WorkspaceLeaf, TFolder, FrontMatterCache } from 'obsidian';

import { SettingTab } from './src/SettingTab';
import type { PluginSettings } from './src/SettingTab';

import { IdSuggestModal } from './src/IdSuggestModal';
import { RandomNoteView, RANDOM_NOTE_VIEW_TYPE } from './src/RandomNoteView';

import { getAllTFilesInFolder, getHash } from './src/utils';
import * as AnkiConnect from './src/anki/AnkiConnect';

import { Evernote } from './src/interface/evernote-interface';
import { AnkiConnectNote, AnkiConnectNoteAndID } from './src/interface/anki-note-interface';
import { AnkiConnectRequest } from './src/anki/AnkiConnect';

const DEFAULT_SETTINGS: PluginSettings = {
	newNoteFolderPath: 'note',
	randomNoteCount: 10,
	randomNoteIdPrefixes: ''
}

// 插件主类 (不变)
export default class EvernotePlugin extends Plugin {
	settings: PluginSettings;
	evernotes: Evernote[];

	add_note_requests: AnkiConnectRequest[] = []
	edit_note_requests: AnkiConnectRequest[] = []
	delete_note_requests: AnkiConnectRequest[] = []


	async onload() {
		await this.loadSettings();
		this.addSettingTab(new SettingTab(this.app, this));

		// 注册随机笔记视图
		this.registerView(
			RANDOM_NOTE_VIEW_TYPE,
			(leaf) => new RandomNoteView(leaf, this)
		);

		this.addRibbonIcon('sticky-note', 'Create Note with ID', () => {
			new IdSuggestModal(this.app, this).open();
		});

		this.addCommand({
			id: 'create-note-with-next-id',
			name: 'Create note with next ID',
			callback: () => {
				new IdSuggestModal(this.app, this).open();
			},
		});

		this.addCommand({
			id: 'open-random-note-panel',
			name: 'Open Random Note Panel',
			callback: () => {
				this.activateRandomNoteView();
			},
		});

		this.addCommand({
			id: "sync-to-anki",
			name: "Sync to Anki",
			callback: async () => {
				await this.syncToAnki();
			}
		})
	}

	async activateRandomNoteView() {
		const { workspace } = this.app;

		let leaf: WorkspaceLeaf | null = null;
		const leaves = workspace.getLeavesOfType(RANDOM_NOTE_VIEW_TYPE);

		if (leaves.length > 0) {
			// 如果视图已经存在，激活它
			leaf = leaves[0];
		} else {
			// 创建新的视图
			leaf = workspace.getRightLeaf(false);
			if (leaf) {
				await leaf.setViewState({ type: RANDOM_NOTE_VIEW_TYPE, active: true });
			}
		}

		if (leaf) {
			workspace.revealLeaf(leaf);
		}
	}

	async syncToAnki() {
		new Notice("Preparing to Sync to Anki...");
		try {
			await AnkiConnect.invoke('modelNames')
		}
		catch (e) {
			new Notice("Anki is not running or is not connected. Please start Anki and try again.");
			return;
		}
		new Notice("Successfully connected to Anki!");

		const scanDir = this.app.vault.getAbstractFileByPath("/");
		if (scanDir && scanDir instanceof TFolder) {
			let markdownFiles = []
			console.log("Using custom scan directory: " + scanDir.path);
			const allFiles = getAllTFilesInFolder(scanDir);
			// 只保留 markdown 文件
			markdownFiles = allFiles.filter(file => file.extension === 'md');
			console.log("Found markdown files: ", markdownFiles.length);

			this.parseEvernote(markdownFiles)
			this.syncAnki()
		}
	}

	onunload() { }


	async loadSettings() {
		this.settings = Object.assign({}, DEFAULT_SETTINGS, await this.loadData());
	}

	async saveSettings() {
		await this.saveData(this.settings);
	}

	async parseEvernote(files: TFile[]) {
		this.evernotes = []
		this.add_note_requests = []
		console.log("Parsing files: ", files.length);
		for (let file of files) {
			let title = file.basename
			let fileCache = this.app.metadataCache.getFileCache(file)
			let frontmatter = fileCache?.frontmatter

			let content = await this.app.vault.cachedRead(file)
			let body = content
			if (fileCache && frontmatter) {
				const frontmatterEndPos = fileCache.frontmatterPosition?.end.offset
				body = content.slice(frontmatterEndPos).trim()
			}

			// if (!frontmatter || !frontmatter.id) return;

			let evernote: Evernote = {
				title: title,
				content: body,
				frontmatter: {
					id: frontmatter?.id || "",
					hash: frontmatter?.hash || getHash(file),
					status: frontmatter?.status || "unsynced",
					deck_name: frontmatter?.deck_name || ""
				}
			}
			this.evernotes.push(evernote)
		}

		console.log("Total evernotes parsed: ", this.evernotes.length);
		for (let evernote of this.evernotes) {
			if (evernote.frontmatter.status == "unsynced") {
				let request = this.request('addNote', {
					note: {
						deckName: "Ever",
						modelName: "Evernote",
						fields: {
							"Front": evernote.title,
							"Back": evernote.content
						},
						options: {
							allowDuplicate: false,
							duplicateScope: "deck"
						},
						tags: ["Obsidian_to_Anki"]
					}
				})
				this.add_note_requests.push(request)
			}
		}
		console.log("Unsynced notes to add: ", this.add_note_requests.length);

	}

	request(action: string, params = {}): AnkiConnectRequest {
			return { action, version: 6, params }
		}

	
	async syncAnki(){
			let requests: AnkiConnect.AnkiConnectRequest[] = []
			requests.push(AnkiConnect.multi(this.add_note_requests))

			console.log("Add note requests: ", this.add_note_requests.length);
			console.log("Requests to send: ", requests);
			let result = await AnkiConnect.invoke('multi', {actions: requests})
			console.log("Anki result: ", result);
		}
	}